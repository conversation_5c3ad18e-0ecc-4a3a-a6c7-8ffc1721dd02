<route lang="jsonc" type="page">
{
  "layout": "default",
  "style": {
    "navigationBarTitleText": "待办"
  }
}
</route>

<script lang="ts" setup>
import type { TagType } from 'wot-design-uni/components/wd-tag/types'
import { projectInfo } from '@/api/business'

defineOptions({
  name: 'Todo',
})

// 业务数据接口
interface TodoCategory {
  id: string
  name: string
  projectStatus: ProjectStatus
  items: ProjectInfoItem[]
}

// 样式配置接口
interface TodoTheme {
  id: string
  color: string
  color50Opacity: string
  color5Opacity: string
}

// 样式配置数据（静态配置）
const todoThemes: Record<string, TodoTheme> = {
  'image-pending': {
    id: 'image-pending',
    color: 'rgba(0, 133, 208, 1)',
    color50Opacity: 'rgba(0, 133, 208, 0.5)',
    color5Opacity: 'rgba(0, 133, 208, 0.05)',
  },
  'appeal-pending': {
    id: 'appeal-pending',
    color: 'rgb(214, 146, 81)',
    color50Opacity: 'rgba(214, 146, 81, 0.5)',
    color5Opacity: 'rgba(214, 146, 81, 0.05)',
  },
  'info-pending': {
    id: 'info-pending',
    color: '#3DCCCC',
    color50Opacity: '#3DCCCC80',
    color5Opacity: '#3DCCCC0D',
  },
}

// 业务数据配置（可动态修改）
const todoCategories = ref<TodoCategory[]>([
  {
    id: 'image-pending',
    name: '图片待提交',
    projectStatus: '图像待提交',
    items: [],
  },
  {
    id: 'appeal-pending',
    name: '用户申诉',
    projectStatus: '用户申诉',
    items: [],
  },
  {
    id: 'info-pending',
    name: '待阅信息',
    projectStatus: '已结束',
    items: [],
  },
])

// 获取主题样式的计算属性
function getTodoTheme(categoryId: string): TodoTheme {
  return todoThemes[categoryId] || todoThemes['image-pending']
}

function handleHeaderClick(category: TodoCategory) {
  uni.navigateTo({
    url: `/pages/list/index?projectStatus=${category.projectStatus}`,
  })
}

function handleItemClick(category: TodoCategory, item: ProjectInfoItem) {
  uni.navigateTo({
    url: `/pages/detail/index?id=${item.id}&=projectStatus${category.projectStatus}`,
  })
}

function getTagType(projectStatus: ProjectStatus): TagType {
  return 'warning'
}

async function getProjectInfo(type: ProjectStatus) {
  const res = await projectInfo({
    pageNum: 1,
    pageSize: 3,
    projectStatus: type,
  })
  console.log(res?.result?.list)
  return res?.result?.list || []
}

async function init() {
  for (const category of todoCategories.value) {
    const data = await getProjectInfo(category.projectStatus)
    category.items = data
  }
}

onShow(() => {
  init()
})
</script>

<template>
  <!-- TODO 布局溢出，带滚动条 -->
  <view class="global-bg-image-bg" min-h-screen flex flex-col items-center pt-20rpx>
    <wd-card
      v-for="category in todoCategories"
      :key="category.id"
      custom-class="w-650rpx"
      custom-content-class="text-black"
      border-y="3px solid rd-5px"
      :style="{ borderColor: getTodoTheme(category.id).color }"
    >
      <template #title>
        <view
          flex items-center justify-between
          p-b-10px
          border-b="1px solid rd-5px"
          :style="{ borderColor: getTodoTheme(category.id).color }"
          @click="handleHeaderClick(category)"
        >
          <text>
            {{ category.name }}
          </text>
          <text text-sm text-gray>
            更多&gt;&gt;
          </text>
        </view>
      </template>

      <!-- 当该分类有数据时显示列表 -->
      <template v-if="category.items.length > 0">
        <view
          v-for="(item, index) in category.items"
          :key="item.roomName + index"
          border-b="1px solid"
          my-10px flex items-center justify-between border-rd-5px px-3px py-5px first:mt-0
          :style="{
            backgroundColor: getTodoTheme(category.id).color5Opacity,
            borderColor: getTodoTheme(category.id).color50Opacity,
          }"
          @click="handleItemClick(category, item)"
        >
          <text>
            {{ item.roomName }}
          </text>
          <view v-if="category.id === 'appeal-pending'">
            <wd-tag :type="getTagType(item.projectStatus)" plain>
              {{ item.projectStatus }}
            </wd-tag>
          </view>
        </view>
      </template>

      <!-- 当该分类无数据时显示空状态提示 -->
      <template v-else>
        <view class="empty-content" flex flex-col items-center justify-center p-y-40rpx>
          <wd-status-tip
            image="content"
            tip="暂无数据"
            :image-size="{ width: 120, height: 90 }"
          />
        </view>
      </template>
    </wd-card>
  </view>
</template>

<style lang="scss" scoped>
//
</style>
