<route lang="jsonc" type="page">
{
  "layout": "default",
  "style": {
    "navigationBarTitleText": "项目管理"
  }
}
</route>

<script lang="ts" setup>
import type { LoadMoreState } from 'wot-design-uni/components/wd-loadmore/types'
import type { TagType } from 'wot-design-uni/components/wd-tag/types'
import type { ProjectInfoQuery } from '@/api/business'

import { useQuery } from '@tanstack/vue-query'
import { projectInfo } from '@/api/business'

defineOptions({
  name: 'List',
})

// 项目查询参数
const projectQuery = ref<ProjectInfoQuery>({
  pageNum: 1,
  pageSize: 5,
})

// 项目列表数据
const projectList = ref<ProjectInfoItem[]>([])
// 总数据量
const total = ref(0)

// 使用useQuery获取项目信息
const {
  data,
  isLoading,
  error,
  refetch,
} = useQuery({
  queryKey: ['projectInfo', projectQuery],
  queryFn: async () => {
    uni.setNavigationBarTitle({
      title: `项目管理${projectQuery.value.projectStatus ? `-${projectQuery.value.projectStatus}` : ''}`,
    })
    const res = await projectInfo(projectQuery.value)
    return res?.result || { list: [], total: 0 }
  },
  // enabled: computed(() => !!projectQuery.value),
})

// 计算加载更多状态，主要由isLoading控制
const loadMoreState = computed<LoadMoreState>(() => {
  // 如果有错误，显示错误状态
  if (error.value) {
    return 'error'
  }
  // 如果数据已全部加载完成，显示完成状态
  if (!isLoading.value && projectList.value.length >= total.value && total.value > 0) {
    return 'finished'
  }
  // 如果正在加载或还有更多数据，显示加载状态
  return 'loading'
})

// 监听数据变化，更新项目列表
watch(data, (newData) => {
  if (newData) {
    if (projectQuery.value.pageNum === 1) {
      // 首次加载或刷新，替换数据
      projectList.value = newData.list || []
    }
    else {
      // 加载更多，追加数据
      projectList.value = [...projectList.value, ...(newData.list || [])]
    }
    total.value = newData.total || 0
  }
}, { immediate: false })

// 页面参数变化时重新获取数据
onLoad((options) => {
  if (options.projectStatus) {
    projectQuery.value.projectStatus = options.projectStatus as ProjectStatus
    projectQuery.value.pageNum = 1
    refetch()
  }
})

// 处理查询条件点击
function handleQueryClick() {
  uni.showToast({
    title: '填写查询条件',
    icon: 'none',
  })
}

// 处理项目新增点击
function handleAddProject() {
  uni.navigateTo({
    url: `/pages/newproject/index`,
  })
}

// 处理项目项点击
function handleProjectClick(project: ProjectInfoItem) {
  uni.navigateTo({
    url: `/pages/detail/index?id=${project.id}`,
  })
}

// 获取机房类型标签样式
function getTypeTagType(type: string): TagType {
  // return type === '汇聚机房' ? 'primary' : 'default'
  return 'primary'
}

// 获取任务状态标签样式
function getStatusTagType(status: string): TagType {
  switch (status) {
    case '图像待提交':
      return 'warning'
    case '待审核':
      return 'primary'
    case '已完成':
      return 'success'
    default:
      return 'warning'
  }
}

// 加载更多数据
function handleLoadMore() {
  if (!isLoading.value && !error.value && projectList.value.length < total.value) {
    projectQuery.value.pageNum += 1
    console.log('load more')
    refetch()
  }
}

// 重新加载（错误状态时点击重试）
function handleReload() {
  refetch()
}

// 页面触底时加载更多
onReachBottom(() => {
  handleLoadMore()
})

// 监听需要刷新列表的事件
onMounted(() => {
  uni.$on('projectAdded', handleRefreshList)
})

// 页面卸载时移除事件监听
onUnmounted(() => {
  uni.$off('projectAdded', handleRefreshList)
})

// 刷新列表数据
function handleRefreshList() {
  // 重置到第一页并重新加载数据
  projectQuery.value.pageNum = 1
  refetch()
}
</script>

<template>
  <view min-h-screen>
    <!-- 头部区域 -->
    <view v-if="0" border-rd="0 0 20rpx 20rpx" m-b-20rpx bg-white bg-opacity-90 p-x-30rpx p-t-20rpx>
      <!-- 项目管理标题和统计 -->
      <view v-if="0" m-b-20rpx flex items-center justify-between>
        <text flex-1 text-32rpx font-bold color="#303133">
          项目管理(共{{ total }}条)
        </text>
        <wd-button
          type="primary"
          size="small"
          :round="false"
          :loading="isLoading && projectQuery.pageNum === 1"
          @click="handleQueryClick()"
        >
          填写查询条件
        </wd-button>
      </view>

      <!-- 项目新增按钮 -->
      <view m-b-30rpx>
        <wd-button
          type="success"
          size="small"
          :round="false"
          @click="handleAddProject"
        >
          项目新增
        </wd-button>
      </view>
    </view>

    <view m-x-16rpx m-t-32rpx>
      <HeaderLabel :title="`项目管理(共${total}条)`" w-718rpx>
        <wd-button
          type="info"
          size="small"
          :round="false"
          :loading="isLoading && projectQuery.pageNum === 1"
          @click="handleQueryClick()"
        >
          填写查询条件
        </wd-button>
      </HeaderLabel>

      <!-- 项目新增按钮 -->
      <view m-y-16rpx>
        <wd-button
          type="success"
          size="small"
          :round="false"
          @click="handleAddProject"
        >
          项目新增
        </wd-button>
      </view>
    </view>

    <!-- 项目列表 -->
    <view>
      <!-- 首次加载时的loading状态 -->
      <template v-if="isLoading && projectQuery.pageNum === 1 && projectList.length === 0">
        <view min-h-400px flex flex-col items-center justify-center p-x-30rpx>
          <wd-loading size="40rpx" />
          <text m-t-20rpx text-28rpx color="#999">加载中...</text>
        </view>
      </template>

      <!-- 当有项目数据时显示列表 -->
      <template v-else-if="projectList.length > 0">
        <Card
          v-for="project in projectList"
          :key="project.id"
          @click="handleProjectClick(project)"
        >
          <!-- 机房名称标题 -->
          <template #title>
            {{ project.roomName }}
          </template>

          <!-- 项目详情内容 -->
          <view>
            <LabelValue label="编号" :value="project.roomCode" />
            <LabelValue label="机房地址" :value="project.roomAddress" />
            <LabelValue label="机房类型" :value="project.roomType" />
            <LabelValue label="任务状态">
              <wd-tag
                plain
                :type="getStatusTagType(project.projectStatus || '')"
              >
                {{ project.projectStatus }}
              </wd-tag>
            </LabelValue>
          </view>
        </Card>

        <!-- 加载更多组件 -->
        <wd-loadmore
          :state="loadMoreState"
          loading-text="加载中..."
          finished-text="没有更多了"
          error-text="加载失败，点击重试"
          @reload="handleReload"
        />
      </template>

      <!-- 当无项目数据时显示空状态提示 -->
      <template v-else-if="!isLoading">
        <view min-h-400px flex flex-col items-center justify-center p-x-30rpx>
          <wd-status-tip
            image="content"
            tip="暂无项目数据"
            :image-size="{ width: 200, height: 150 }"
          />
        </view>
      </template>
    </view>
  </view>
</template>
