<route lang="jsonc" type="page">
{
  "layout": "default",
  "style": {
    "navigationBarTitleText": "工单详情"
  }
}
</route>

<script lang="ts" setup>
import type { TagType } from 'wot-design-uni/components/wd-tag/types'
import { useQuery } from '@tanstack/vue-query'
import { projectDelete, projectDetail } from '@/api/business'
import CollapsePanelItem from '@/components/CollapsePanelItem/CollapsePanelItem.vue'
import UploadItem from '@/components/UploadItem/UploadItem.vue'

defineOptions({
  name: 'Detail',
})

const uploadItemRef = ref<InstanceType<typeof UploadItem> | null>(null)

// 获取页面参数
const projectId = ref<string>('')

// 页面加载时获取参数
onLoad((options) => {
  if (options.id) {
    projectId.value = options.id
  }
})

// 使用useQuery获取项目详情
const {
  data: projectDetailData,
  isLoading,
  error,
  refetch,
} = useQuery({
  queryKey: ['projectDetail', projectId],
  queryFn: async () => {
    if (!projectId.value) {
      throw new Error('项目ID不能为空')
    }
    const res = await projectDetail(projectId.value)
    return res?.result
  },
  enabled: computed(() => !!projectId.value),
})

const value = ref<string[]>([
  '项目信息',
])

// 获取机房类型标签样式
function getTypeTagType(type: string): TagType {
  return type === '汇聚机房' ? 'primary' : 'primary'
}

// 获取任务状态标签样式
function getStatusTagType(status: string): TagType {
  switch (status) {
    case '图像待提交':
      return 'warning'
    case '待审核':
      return 'primary'
    case '已完成':
      return 'success'
    default:
      return 'warning'
  }
}

function handleEdit() {
  console.log('编辑操作')
  uni.navigateTo({
    url: `/pages/newproject/index?id=${projectId.value}`,
  })
}

function handleDelete() {
  if (!projectId.value) {
    uni.showToast({
      title: '项目ID不存在',
      icon: 'error',
    })
    return
  }

  uni.showModal({
    title: '确认删除',
    content: '确定要删除这个项目吗？删除后无法恢复。',
    confirmText: '删除',
    cancelText: '取消',
    confirmColor: '#ff4757',
    success: (res) => {
      if (res.confirm) {
        // 用户确认删除
        performDelete()
      }
    },
  })
}

// 执行删除操作
async function performDelete() {
  try {
    const res = await projectDelete(projectId.value)
    if (res.code === 200) {
      uni.showToast({
        title: '删除成功',
        icon: 'success',
      })
      // 触发项目列表刷新事件
      uni.$emit('projectAdded')
      // 返回上一页
      setTimeout(() => {
        uni.navigateBack({
          delta: 1,
        })
      }, 1500)
    }
    else {
      throw new Error(res.message || '删除失败')
    }
  }
  catch (error) {
    console.error('删除项目失败:', error)
    uni.showToast({
      title: '删除失败',
      icon: 'error',
    })
  }
}

function handleSpecification() {
  console.log('图像上传规范')
}
function handleSaveUpload() {
  console.log('保存')
  uploadItemRef.value?.onUploadClick()
}

function handleSubmitUpload() {
  console.log('提交上传')
}

function handleClick(flag: string) {
  switch (flag) {
    case '保存':
      handleSaveUpload()
      break
    case '保存并提交':
      handleSubmitUpload()
      break
    case '图片重新上传':
      handleSaveUpload()
      break
    case '同意':
      console.log('同意')
      break
    case '任务结束':
      console.log('任务结束')
      break
    case '驳回':
      console.log('驳回')
      break
    default:
      break
  }
}

function handleRefreshList() {
  refetch()
}

onMounted(() => {
  uni.$on('projectEdited', handleRefreshList)
})

onUnmounted(() => {
  uni.$off('projectEdited', handleRefreshList)
})
</script>

<template>
  <view class="" p-t-16rpx pb-80px>
    <!-- 加载状态 -->
    <view v-if="isLoading" flex="~ col items-center justify-center" min-h-400px>
      <wd-loading size="40rpx" />
      <text m-t-20rpx text-28rpx color="#999">加载中...</text>
    </view>

    <!-- 错误状态 -->
    <view v-else-if="error" flex="~ col items-center justify-center" min-h-400px>
      <text text-28rpx color="#f56c6c">加载失败</text>
      <wd-button type="primary" size="small" m-t-20rpx @click="refetch">
        重试
      </wd-button>
    </view>

    <!-- 项目详情内容 - 完全加载后才显示 -->
    <template v-else-if="projectDetailData">
      <wd-collapse v-model="value" m-x-16rpx>
        <CollapsePanelItem title="项目信息" name="项目信息">
          <template #header-tools>
            <view flex gap-10px>
              <wd-button type="warning" :round="false" size="small" @click.stop="handleEdit">
                编辑
              </wd-button>
              <wd-button type="error" :round="false" size="small" @click.stop="handleDelete">
                删除
              </wd-button>
            </view>
          </template>
          <!-- 项目详情内容 -->
          <view>
            <view flex="~ items-center wrap" mb-12px>
              <text mr-8px whitespace-nowrap text-14px color-gray-600>机房名称：</text>
              <text flex-1 break-all text-14px color-gray-900>{{ projectDetailData.projectInfoRes?.roomName }}</text>
            </view>

            <view flex="~ items-center wrap" mb-12px>
              <text mr-8px whitespace-nowrap text-14px color-gray-600>编号：</text>
              <text flex-1 break-all text-14px color-gray-900>{{ projectDetailData.projectInfoRes?.roomCode }}</text>
            </view>

            <view flex="~ items-center wrap" mb-12px>
              <text mr-8px whitespace-nowrap text-14px color-gray-600>机房地址：</text>
              <text flex-1 break-all text-14px color-gray-900>{{ projectDetailData.projectInfoRes?.roomAddress }}</text>
            </view>

            <view flex="~ items-center wrap" mb-12px>
              <text mr-8px whitespace-nowrap text-14px color-gray-600>机房类型：</text>
              <wd-tag :type="getTypeTagType(projectDetailData.projectInfoRes?.roomType || '')" plain>
                {{ projectDetailData.projectInfoRes?.roomType }}
              </wd-tag>
              <text ml-20px mr-8px whitespace-nowrap text-14px color-gray-600>项目状态：</text>
              <wd-tag :type="getStatusTagType(projectDetailData.projectInfoRes?.projectStatus || '')" plain>
                {{ projectDetailData.projectInfoRes?.projectStatus }}
              </wd-tag>
            </view>

            <view flex="~ items-center wrap" mb-12px>
              <text mr-8px whitespace-nowrap text-14px color-gray-600>框架名称：</text>
              <text flex-1 break-all text-14px color-gray-900>{{ projectDetailData.projectInfoRes?.frameName }}</text>
            </view>

            <view flex="~ items-center wrap" mb-12px>
              <text mr-8px whitespace-nowrap text-14px color-gray-600>执行地市：</text>
              <text flex-1 break-all text-14px color-gray-900>{{ projectDetailData.projectInfoRes?.city }}</text>
            </view>

            <view flex="~ items-center wrap" mb-12px>
              <text mr-8px whitespace-nowrap text-14px color-gray-600>施工单位：</text>
              <text flex-1 break-all text-14px color-gray-900>{{ projectDetailData.projectInfoRes?.buildUnit }}</text>
            </view>

            <view flex="~ items-center wrap" mb-12px>
              <text mr-8px whitespace-nowrap text-14px color-gray-600>区公司机房建设负责人：</text>
              <text mr-8px break-all text-14px color-gray-900>{{ projectDetailData.projectInfoRes?.districtManager }}</text>
            </view>
            <view flex="~ items-center wrap" mb-12px>
              <text mr-8px whitespace-nowrap text-14px color-gray-600>地市机房建设负责人：</text>
              <text flex-1 break-all text-14px color-gray-900>{{ projectDetailData.projectInfoRes?.cityManager }}</text>
            </view>

            <view flex="~ items-center wrap" mb-12px>
              <text mr-8px whitespace-nowrap text-14px color-gray-600>监理单位：</text>
              <text flex-1 break-all text-14px color-gray-900>{{ projectDetailData.projectInfoRes?.supervisionUnit }}</text>
            </view>

            <view flex="~ items-center wrap" mb-12px>
              <text mr-8px whitespace-nowrap text-14px color-gray-600>监理员：</text>
              <text mr-8px break-all text-14px color-gray-900>{{ projectDetailData.projectInfoRes?.supervisor }}</text>
              <text ml-20px mr-8px whitespace-nowrap text-14px color-gray-600>施工队长：</text>
              <text flex-1 break-all text-14px color-gray-900>{{ projectDetailData.projectInfoRes?.buildLeader }}</text>
            </view>

            <view flex="~ items-center wrap" mb-12px>
              <text mr-8px whitespace-nowrap text-14px color-gray-600>收集完成时限：</text>
              <text flex-1 break-all text-14px color-gray-900>{{ projectDetailData.projectInfoRes?.finishTime }}</text>
            </view>

            <view flex="~ items-center wrap" mb-12px>
              <text mr-8px whitespace-nowrap text-14px color-gray-600>创建人：</text>
              <text flex-1 break-all text-14px color-gray-900>{{ projectDetailData.projectInfoRes?.createBy }}</text>
            </view>

            <view flex="~ items-center wrap">
              <text mr-8px whitespace-nowrap text-14px color-gray-600>创建时间：</text>
              <text flex-1 break-all text-14px color-gray-900>{{ projectDetailData.projectInfoRes?.createTime }}</text>
            </view>
          </view>
        </CollapsePanelItem>

        <CollapsePanelItem title="图片/视频上传" name="图片/视频上传">
          <template v-if="1" #header-tools>
          <!-- <wd-button type="info" :round="false" plain size="small" @click.stop="handleSpecification">
            图像上传规范
          </wd-button> -->
          </template>
          <view>
            <UploadItem ref="uploadItemRef" />
          </view>
        </CollapsePanelItem>
      </wd-collapse>

      <view shadow="0 -2px 8px rgba(0,0,0,0.1)" fixed bottom-0 left-0 right-0 z-100 bg-white p-16px>
        <!-- 图像待提交 -->
        <view v-if="projectDetailData.projectInfoRes?.projectStatus === '图像待提交'">
          <view flex="~" gap-20rpx>
            <wd-button type="success" :round="false" plain block flex-1 @click="handleClick('保存')">
              保存
            </wd-button>
            <wd-button type="success" :round="false" block flex-1 @click="handleClick('保存并提交')">
              保存并提交
            </wd-button>
          </view>
        </view>
        <!-- AI识别待确认 -->
        <template v-else-if="projectDetailData.projectInfoRes?.projectStatus === 'AI识别待确认'">
          <view flex="~" gap-0rpx>
            <wd-button type="primary" :round="false" flex-1 @click="handleClick('图片重新上传')">
              图片重新上传
            </wd-button>
            <wd-button type="warning" :round="false" flex-1 @click="handleClick('同意')">
              同意
            </wd-button>
            <wd-button type="success" :round="false" flex-1 @click="handleClick('任务结束')">
              结束
            </wd-button>
          </view>
        </template>
        <!-- 申诉待审批 -->
        <view v-else-if="projectDetailData.projectInfoRes?.projectStatus === '申诉待审批'">
          <view flex="~" gap-20rpx>
            <wd-button type="error" :round="false" block flex-1 @click="handleClick('驳回')">
              驳回
            </wd-button>
            <wd-button type="success" :round="false" block flex-1 @click="handleClick('同意')">
              同意
            </wd-button>
          </view>
        </view>
      </view>
    </template>
  </view>
</template>

<style lang="scss" scoped>
//
</style>
