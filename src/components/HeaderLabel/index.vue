<script lang="ts" setup>
defineOptions({
  name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
})

const props = withDefaults(defineProps<Props>(), {
  title: '',
})

interface Props {
  /* 标题 */
  title?: string
}
</script>

<template>
  <view class="header-label" h-64rpx flex items-center justify-between bg-white py-12rpx>
    <text border-l="2px solid #0085D0" px-12rpx text-36rpx>{{ props.title }}</text>
    <view mr-12rpx flex items-center>
      <slot />
    </view>
  </view>
</template>

<style lang="scss" scoped>
//
</style>
