<script lang="ts" setup>
defineOptions({
  name: 'LabelValue',
})

const props = withDefaults(defineProps<Props>(), {
  label: '',
  labelClass: '',
  value: '',
  valueClass: '',
})

interface Props {
  label?: string
  labelClass?: string
  value?: string
  valueClass?: string
}
</script>

<template>
  <view class="label-value" m-t-16rpx flex>
    <view :class="props.labelClass" flex-shrink-0 text-28rpx color="#65676B">
      <slot name="label">
        {{ props.label }}
      </slot>
    </view>
    ：
    <view :class="props.valueClass" flex-1 text-28rpx color="#303133">
      <slot />
      <slot name="value">
        {{ props.value }}
      </slot>
    </view>
  </view>
</template>

<style lang="scss" scoped>
//
</style>
