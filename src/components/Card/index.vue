<script lang="ts" setup>
defineOptions({
  name: 'Card',
})

const props = withDefaults(defineProps<Props>(), {
  title: '',
  titleClass: '',
})

const emit = defineEmits(['on-click'])

interface Props {
  title?: string
  titleClass?: string
}

function handleClick() {
  emit('on-click')
}
</script>

<template>
  <view class="component-card" m-20rpx bg-white p-16rpx @click="handleClick">
    <view class="card-title" :class="props.titleClass" style="background-color:rgba(0, 133, 208, 0.15)" p-x-12rpx p-y-16rpx text-32rpx>
      <slot name="title">
        {{ title }}
      </slot>
    </view>
    <view class="card-content">
      <slot />
    </view>
  </view>
</template>

<style lang="scss" scoped>
//
</style>
